/*
 * ====================================================================
 * ESTILOS GLOBAIS E DE CHECKOUT
 * ====================================================================
 * 
 * 1.  **Estrutura do Arquivo**:
 *     - Importação de Variáveis
 *     - Estilos Gerais e Resets
 *     - Acordeão do Resumo do Pedido
 *     - Campos do Formulário (Billing Fields)
 *     - Seção de Pagamento (Geral)
 *     - Método de Pagamento: PIX
 *     - Método de Pagamento: Cartão (Custom)
 *     - Order Bump
 *     - Página de Agradecimento (Thank You Page)
 *     - Media Queries (Responsividade)
 * 
 * 2.  **Metodologia**:
 *     - As cores, fontes e bordas são controladas pelo arquivo `variables.css`.
 *     - Os seletores foram simplificados e aninhamentos desnecessários removidos.
 *     - Comentários explicam cada seção principal.
 *     - Media queries foram agrupadas no final para melhor organização.
 * 
 * ====================================================================
 */

/* --- Importação de Variáveis --- */
@import url('variables.css');

/*
 * ====================================================================
 * ESTILOS GERAIS E RESETS
 * ====================================================================
 */

/* Remove padding de elementos do Elementor */
.elementor-366 .elementor-element.elementor-element,
.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    padding: 0 !important;
    border: none !important;
}

/* Oculta elementos visuais desnecessários */
.elementor-element.elementor-element-6a543efd.elementor-widget.elementor-widget-image,
.wcf-collapsed-order-review-section,
.mp-col-md-4, 
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description,
ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details,
p.mp-details-title,
.payment_box.payment_method_woo-mercado-pago-pix .mp-checkout-pix-terms-and-conditions,
span.wcf-field-required-error,
div#mp-doc-div,
p#mp-security-code-info {
    display: none !important;
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
}

/*
 * ====================================================================
 * ACORDEÃO DO RESUMO DO PEDIDO (MOBILE)
 * ====================================================================
 */

.accordion-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--cor-fundo-principal);
    border-radius: var(--raio-borda-padrao);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    padding: 16px;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box;
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    width: 100%;
}

.header-content, .header-left, .header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left span, .header-right span.total-amount {
    color: var(--cor-texto-preto);
    font-weight: 600;
}

.chevron-icon {
    transition: transform 0.3s ease;
}

.accordion-content {
    display: none;
    padding-top: 16px;
    width: 100%;
}

.accordion-wrapper.active .accordion-content {
    display: block !important;
}

.accordion-wrapper.active .chevron-icon {
    transform: rotate(180deg);
}

/* Tabela de revisão do pedido */
table.shop_table.woocommerce-checkout-review-order-table {
    background-color: var(--cor-fundo-tabela-review);
    border-radius: var(--raio-borda-pequeno);
}

.cartflows_table th, .cartflows_table td {
    padding: 12px;
    text-align: left;
}

.wcf-product-thumbnail img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

/*
 * ====================================================================
 * CAMPOS DO FORMULÁRIO (BILLING FIELDS)
 * ====================================================================
 */

.wcf-customer-info-main-wrapper {
    border: 1px solid var(--cor-borda-wrapper) !important;
    background: var(--cor-fundo-principal);
    padding: 0 28px 28px !important;
}

h3#billing_fields_heading {
    padding-bottom: 12px;
    margin: 22px 0 0 !important;
}

.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper {
  display: block !important;
}

.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > p.form-row {
   order: initial !important;
   position: static !important;
   float: none !important;
   margin-top: initial !important;
}

/* Cor do texto dos inputs */
input#billing_first_name,
input#billing_cpf_cnpj,
input#billing_cellphone,
input#billing_email {
    color: var(--cor-texto-preto);
}

/*
 * ====================================================================
 * SEÇÃO DE PAGAMENTO (GERAL)
 * ====================================================================
 */

#payment {
    background-color: var(--cor-fundo-principal);
    padding: 20px;
    border-radius: var(--raio-borda-padrao);
    box-shadow: var(--sombra-checkout);
    margin-top: 20px;
}

#order_review_heading {
    margin: 22px 0 0 !important;
}

ul.wc_payment_methods.payment_methods.methods {
    border-radius: var(--raio-borda-padrao) !important;
}

/* Estilo base para cada método de pagamento */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not(.woocommerce-info) {
    padding: 14px 16px 0 !important;
    min-height: 0 !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:last-child {
    border-top: none !important;
}

/* Labels dos métodos de pagamento */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {
    font-weight: 500;
    width: calc(100% - 10%);
    color: var(--wcf-payment-section-label-color);
    font-size: 15px;
    display: none !important;
}

/* Estilo do método de pagamento selecionado */
.wc_payment_method > input[type="radio"]:checked + label {
    border: 1px solid var(--cor-borda-selecionada) !important;
    background-color: var(--cor-fundo-selecionado) !important;
}

/* Caixa de detalhes que abre ao selecionar um método */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border: none !important;
    border-top: none !important;
}

/* Aviso de pagamento seguro */
.secure-payment-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--cor-primaria);
    margin: 15px 0;
    font-size: 14px;
    line-height: 1.4;
    gap: 8px;
}

.secure-payment-notice svg {
    flex-shrink: 0;
}

/*
 * ====================================================================
 * MÉTODO DE PAGAMENTO: PIX
 * ====================================================================
 */

/* Reset do container LI */
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 0 20px 0 !important;
    list-style: none !important;
}

/* Label de seleção do PIX */
li.payment_method_woo-mercado-pago-pix label {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 14px 22px !important;
    border: 1px solid var(--cor-borda-secundaria) !important;
    border-radius: var(--raio-borda-padrao) !important;
    cursor: pointer;
    background-color: var(--cor-fundo-principal) !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    color: var(--cor-texto-preto) !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    margin-bottom: 15px !important;
}

li.payment_method_woo-mercado-pago-pix label img {
    width: 22px !important;
    height: 22px !important;
    margin-right: 12px !important;
}

/* Caixa de detalhes do PIX */
.payment_box.payment_method_woo-mercado-pago-pix {
    padding: 35px 25px !important;
    background-color: var(--cor-fundo-secundario) !important;
    border: 1px solid var(--cor-borda-sutil) !important;
    border-radius: var(--raio-borda-padrao) !important;
    margin-top: 0 !important;
    text-align: center !important;
    box-shadow: var(--sombra-sutil) !important;
}

.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-container {
    border: none !important;
    padding: 0 !important;
    background-color: var(--cor-fundo-transparente) !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-image {
    width: 65px !important;
    height: 65px !important;
    margin: 0 auto 25px auto !important;
}

.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-title {
    font-size: 1.25em !important;
    font-weight: 600 !important;
    color: var(--cor-texto-cinza-escuro) !important;
    margin: 0 0 12px 0 !important;
    line-height: 1.4 !important;
}

.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle {
    font-size: 1em !important;
    color: var(--cor-texto-discreto) !important;
    line-height: 1.6 !important;
    margin: 0 auto !important;
    max-width: 90% !important;
}

/*
 * ====================================================================
 * MÉTODO DE PAGAMENTO: CARTÃO (CUSTOM)
 * ====================================================================
 */

li.wc_payment_method.payment_method_woo-mercado-pago-custom label {
    display: none !important;
}

.mp-checkout-custom-container {
    padding: 0 !important;
}

.mp-checkout-custom-card-form .mp-checkout-custom-card-row,
div#mp-card-holder-div {
    padding-bottom: 16px !important;
}

/*
 * ====================================================================
 * ORDER BUMP
 * ====================================================================
 */

.wcf-bump-order-wrap .wcf-content-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important;
    gap: 20px !important;
    background-color: var(--cor-fundo-principal) !important;
    border-radius: var(--raio-borda-padrao) !important;
    padding: 25px !important;
    box-shadow: var(--sombra-card) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.wcf-bump-order-wrap .wcf-bump-order-offer-content-left.wcf-bump-order-image {
    flex-shrink: 0 !important;
    margin: 0 !important;
}

.wcf-bump-order-wrap .wcf-image {
    display: block !important;
    max-width: 100%;
    height: auto;
    border-radius: var(--raio-borda-pequeno);
}

.wcf-bump-order-wrap .wcf-bump-order-offer-content-right {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.wcf-bump-order-wrap .wcf-bump-order-offer {
    width: 100% !important;
    margin: 0 0 8px 0 !important;
    order: 1 !important;
    text-align: left !important;
}

.wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
    font-weight: 600 !important;
    color: var(--cor-texto-cinza-escuro) !important;
    display: block !important;
    line-height: 1.3 !important;
}

.wcf-bump-order-wrap .wcf-bump-order-desc {
    order: 2 !important;
    width: 100% !important;
    font-size: 0.95em !important;
    line-height: 1.6 !important;
    color: var(--cor-texto-cinza-medio) !important;
    text-align: left !important;
}

.wcf-bump-order-wrap .wcf-bump-order-desc p {
    margin: 0 !important;
}

.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
    display: block !important;
    color: var(--cor-texto-cinza-claro) !important;
    line-height: 1.2 !important;
    margin-bottom: 12px !important;
}

.wcf-bump-order-wrap .wcf-bump-order-desc p br {
    display: none !important;
}

.wcf-bump-order-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 10px !important;
    padding: 0 !important;
}

.wcf-bump-order-field-wrap label {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    font-size: 1em !important;
    color: var(--cor-texto-discreto) !important;
}

/*
 * ====================================================================
 * PÁGINA DE AGRADECIMENTO (THANK YOU PAGE)
 * ====================================================================
 */

.mp-details-pix {
    max-width: 500px !important;
    margin: 0 auto !important;
    background-color: var(--cor-fundo-principal) !important;
    border-radius: var(--raio-borda-grande) !important;
    overflow: hidden !important;
    font-family: var(--fonte-principal) !important;
    box-shadow: none !important;
}

.mp-row-checkout-pix {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 16px 20px !important;
}

.mp-col-md-8 {
    width: 100% !important;
    padding: 0 !important;
    background-color: var(--cor-fundo-transparente) !important;
    text-align: center !important;
}

.mp-details-title {
    font-size: 22px !important;
    font-weight: 600 !important;
    color: var(--cor-texto-principal) !important;
    text-align: center !important;
    margin-bottom: 25px !important;
}

.mp-details-pix-amount {
    margin-bottom: 30px !important;
    text-align: center !important;
    width: 100% !important;
}

.mp-details-pix-qr {
    font-size: 16px !important;
    color: var(--cor-texto-secundario) !important;
    display: block !important;
    margin-bottom: 8px !important;
}

.mp-details-pix-qr-value {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: var(--cor-texto-principal) !important;
}

.mp-details-pix-qr-title {
    font-size: 16px !important;
    color: var(--cor-texto-secundario) !important;
    margin-bottom: 20px !important;
    width: 100% !important;
}

.mp-details-pix-qr-img {
    width: 280px !important;
    height: 280px !important;
    padding: 15px !important;
    background-color: var(--cor-fundo-principal) !important;
    border: 1px solid var(--cor-borda-principal) !important;
    border-radius: var(--raio-borda-imagem) !important;
    margin: 0 auto 30px !important;
    display: block !important;
    transition: transform 0.3s ease !important;
}

.mp-details-pix-qr-img:hover {
    transform: scale(1.05) !important;
}

.mp-details-pix-container {
    width: 100% !important;
    max-width: 350px !important;
    margin: 0 auto !important;
}

#mp-qr-code {
    width: 100% !important;
    padding: 12px 15px !important;
    border: 1px solid var(--cor-borda-principal) !important;
    border-radius: var(--raio-borda-padrao) !important;
    font-size: 14px !important;
    color: var(--cor-texto-secundario) !important;
    background-color: var(--cor-fundo-discreto) !important;
    margin-bottom: 15px !important;
    text-align: center !important;
}

.mp-details-pix-button {
    width: 100% !important;
    padding: 14px 20px !important;
    background-color: var(--cor-sucesso) !important;
    color: var(--cor-texto-claro) !important;
    border: none !important;
    border-radius: var(--raio-borda-padrao) !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.mp-details-pix-button:hover {
    background-color: var(--cor-sucesso-hover) !important;
}

.mp-details-pix-button::before {
    content: "" !important;
    display: inline-block !important;
    width: 18px !important;
    height: 18px !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    margin-right: 8px !important;
}

.mp-row-checkout-pix::after {
    content: "Tempo restante: 30 minutos" !important;
    display: block !important;
    margin-top: 0px !important;
    font-size: 14px !important;
    color: var(--cor-texto-secundario) !important;
    text-align: center !important;
    width: 100% !important;
}

/* Tabela de detalhes do pedido */
.woocommerce-order-overview {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    background-color: var(--cor-fundo-secundario) !important;
    padding: 15px !important;
    margin: 0 0 30px 0 !important;
    border: none !important;
}

.woocommerce-order-overview li {
    flex: 1 1 45% !important;
    margin: 5px 0 !important;
    padding: 8px !important;
    border: none !important;
    font-size: 14px !important;
    color: var(--cor-texto-secundario) !important;
    display: flex !important;
    flex-direction: column !important;
}

.woocommerce-order-overview li strong {
    font-size: 16px !important;
    color: var(--cor-texto-principal) !important;
    font-weight: 600 !important;
    margin-top: 4px !important;
}

/* Status da transação */
#transaction-status-message {
    background-color: var(--cor-fundo-secundario) !important;
    padding: 15px !important;
    margin: 20px auto !important;
    max-width: 400px !important;
}

#transaction-status-message div {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#transaction-status-message img {
    width: 24px !important;
    height: 24px !important;
    margin-right: 10px !important;
}

#transaction-status-message p {
    margin: 0 !important;
    color: var(--cor-texto-secundario) !important;
    font-size: 15px !important;
    font-weight: 500 !important;
}


/*
 * ====================================================================
 * MEDIA QUERIES (RESPONSIVIDADE)
 * ====================================================================
 */

/* --- Telas Maiores (Desktop) --- */
@media (min-width: 768px) {
    /* Checkout */
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not(.woocommerce-info) {
        padding: 0 24px 0 !important;
        min-height: 225px !important;
    }

    .mp-checkout-custom-card-form .mp-checkout-custom-card-row {
        padding-bottom: 14px !important;
    }

    /* Thank you page */
    img.mp-pix-template-image {
        width: 36% !important;
    }

    .woocommerce-order .mp-details-pix-qr-img {
        width: 250px !important;
        height: 250px !important;
    }

    .mp-details-pix-qr-value {
        font-size: 28px !important;
    }

    .mp-pix-template-container {
        padding-bottom: 16px !important;
        padding-top: 0px !important;
    }
}

/* --- Telas Médias e Pequenas (Tablets e Celulares) --- */
@media (max-width: 767.98px) {
    .mp-pix-right {
        margin-bottom: 0 !important;
    }

    .header-right {
        gap: 0 !important;
    }

    .secure-payment-notice {
        flex-direction: column !important;
        text-align: center !important;
    }
    
    .secure-payment-notice svg {
        margin-right: 0 !important;
        margin-bottom: 8px !important;
    }

    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce {
        padding: 0 !important;
    }

    .accordion-wrapper.active .accordion-header {
        border-bottom-color: transparent !important; 
    }

    .accordion-header {
        padding: 0 !important;
    }

    #order_review,
    .woocommerce-checkout-review-order-table {
        display: block !important;
        width: 100% !important;
    }

    .wcf-product-image {
        gap: 0 !important;
    }

    .wcf-embed-checkout-form table.shop_table thead tr th:nth-child(2),
    .wcf-embed-checkout-form table.shop_table tbody tr td:nth-child(2),
    .wcf-embed-checkout-form table.shop_table tfoot tr td:nth-child(2) {
        padding-left: 30px !important;
    }

    table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
        background: #f9fafb;
        border-radius: 4px !important;
    }

    h3#billing_fields_heading {
        margin-top: -0.1px !important;
    }
}

/* --- Telas Pequenas (Celulares) --- */
@media (max-width: 600px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not(.woocommerce-info) {
        padding: 15px 16px 0 !important;
        margin: 0 !important;
    }

    #payment {
        padding: 0;
    }

    .mp-checkout-custom-container,
    div#mp-checkout-custom-installments {
        padding: 0 !important;
    }
}

/* --- Telas Muito Pequenas (Celulares Menores) --- */
@media (max-width: 480px) {
    .mp-row-checkout-pix {
        padding: 25px 15px !important;
    }

    .mp-details-pix {
        padding: 0 !important;
        border-radius: 12px !important;
    }

    .mp-details-pix-qr-value {
        font-size: 24px !important;
    }

    .mp-details-pix-qr-img {
        width: 180px !important;
        height: 180px !important;
    }

    .woocommerce-order-overview li {
        flex: 1 1 100% !important;
    }

    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }

    .wcf-bump-order-wrap .wcf-image {
        width: 60px !important;
    }

    .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
    }

    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price,
    span.woocommerce-Price-amount.amount {
        font-size: 14px !important;
    }

    .wcf-bump-order-wrap .wcf-bump-order-desc p {
        font-size: 0.85em !important;
    }

    .wcf-bump-order-field-wrap {
        padding: 14px !important;
    }
}

@media (max-width: 320px) {
    .woocommerce-order .mp-details-pix-qr-img {
        width: 220px !important;
        height: 220px !important;
        padding: 10px !important;
    }
}
