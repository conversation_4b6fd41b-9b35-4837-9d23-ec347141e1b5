/*
 * ====================================================================
 * JAVASCRIPT PRINCIPAL DO CHECKOUT
 * ====================================================================
 * 
 * 1.  **Estrutura do Arquivo**:
 *     - Funções Utilitárias
 *     - Módulo de Manipulação do Formulário
 *     - Módulo do Acordeão de Resumo do Pedido
 *     - Módulo de Limpeza do Order Bump
 *     - Módulo de Inicialização
 * 
 * 2.  **Metodologia**:
 *     - O código é dividido em módulos para separar responsabilidades.
 *     - As configurações (textos, seletores) são importadas do `config.js`.
 *     - Funções são bem definidas e focadas em uma única tarefa.
 *     - MutationObservers são usados para garantir que as customizações
 *       persistam mesmo com atualizações dinâmicas do DOM (AJAX).
 * 
 * ====================================================================
 */

// Espera o DOM estar completamente carregado
document.addEventListener('DOMContentLoaded', () => {

    /**
     * ====================================================================
     * MÓDULO DE MANIPULAÇÃO DO FORMULÁRIO
     * ====================================================================
     */
    const Formulario = (() => {

        // Altera o placeholder e o comportamento do campo de e-mail
        function ajustarCampoEmail() {
            const emailField = document.querySelector(config.seletores.billingEmail);
            if (emailField) {
                emailField.placeholder = config.textos.emailPlaceholder;
                emailField.addEventListener('focus', () => { emailField.placeholder = ''; });
                emailField.addEventListener('blur', () => {
                    if (!emailField.value) {
                        emailField.placeholder = config.textos.emailPlaceholder;
                    }
                });
            }
        }

        // Altera o título da seção de pagamento
        function alterarTituloPagamento() {
            const paymentHeading = document.querySelector(config.seletores.paymentOptionsHeading);
            if (paymentHeading) {
                paymentHeading.textContent = config.textos.paymentOptionsHeading;
            }
        }

        // Remove os asteriscos de campos obrigatórios
        function removerAsteriscos() {
            const asteriscos = document.querySelectorAll(`${config.seletores.customerDetails} ${config.seletores.requiredSpans}`);
            asteriscos.forEach(span => {
                if (span.textContent.trim() === '*' && span.getAttribute('aria-hidden') === 'true') {
                    span.remove();
                }
            });
        }

        // Reorganiza os campos do formulário
        function organizarCampos() {
            const nameField = document.querySelector(config.seletores.billingFirstName);
            const emailField = document.querySelector(config.seletores.billingEmailField);
            const wrapper = document.querySelector(config.seletores.billingFieldsWrapper);

            if (nameField && emailField && wrapper && nameField.nextElementSibling !== emailField) {
                nameField.insertAdjacentElement('afterend', emailField);
                [nameField, emailField].forEach(field => {
                    field.classList.add('form-row-wide');
                    field.classList.remove('form-row-first', 'wcf-column-50', 'form-row-fill');
                });
            }
        }

        // Remove asteriscos dos placeholders
        function removerAsteriscosPlaceholder() {
            const inputs = document.querySelectorAll(config.seletores.inputsWithPlaceholder);
            inputs.forEach(input => {
                if (input.placeholder.includes('*')) {
                    input.placeholder = input.placeholder.replace(/\s*\*\s*/g, '');
                }
            });
        }

        // Altera o texto "Código de segurança" para "CVV"
        function alterarLabelCVV() {
            const labels = document.querySelectorAll(config.seletores.securityCodeLabel);
            labels.forEach(label => {
                if (label.textContent.includes('Código de segurança')) {
                    label.innerHTML = config.textos.securityCodeLabel;
                }
            });
        }

        // Oculta e neutraliza o campo de CEP
        function gerenciarCampoCEP() {
            const cepFieldWrapper = document.querySelector(config.seletores.billingPostcodeField);
            if (cepFieldWrapper) {
                cepFieldWrapper.style.display = 'none';
            }
        }

        // Função de inicialização do módulo
        function init() {
            ajustarCampoEmail();
            alterarTituloPagamento();
            removerAsteriscos();
            organizarCampos();
            removerAsteriscosPlaceholder();
            alterarLabelCVV();
            gerenciarCampoCEP();

            // Observadores para garantir a persistência das alterações
            const observer = new MutationObserver(() => {
                removerAsteriscos();
                organizarCampos();
                removerAsteriscosPlaceholder();
                alterarLabelCVV();
            });

            const customerDetails = document.querySelector(config.seletores.customerDetails);
            if (customerDetails) {
                observer.observe(customerDetails, { childList: true, subtree: true });
            }

            if (window.jQuery) {
                jQuery(document.body).on('updated_checkout', () => {
                    removerAsteriscos();
                    organizarCampos();
                });
            }
        }

        return { init };

    })();

    /**
     * ====================================================================
     * MÓDULO DO ACORDEÃO DE RESUMO DO PEDIDO
     * ====================================================================
     */
    const AcordeaoResumo = (() => {
        let desktopAccordionInitialized = false;
        let currentObserver = null;

        function updateAccordionHeaderTotal() {
            const totalElement = document.querySelector(`${config.seletores.customOrderSummaryContainer} ${config.seletores.tableTotalAmount}`);
            const headerTotalSpan = document.querySelector(`${config.seletores.customOrderSummaryContainer} ${config.seletores.accordionHeaderTotal}`);
            if (totalElement && headerTotalSpan) {
                headerTotalSpan.textContent = totalElement.textContent;
            }
        }

        function setupMutationObserver() {
            if (currentObserver) currentObserver.disconnect();
            const containerToObserve = document.querySelector(config.seletores.customOrderSummaryContainer);
            if (containerToObserve) {
                currentObserver = new MutationObserver(() => {
                    setTimeout(updateAccordionHeaderTotal, 150);
                });
                currentObserver.observe(containerToObserve, { subtree: true, childList: true, characterData: true });
            }
        }

        function initializeDesktopLayout() {
            if (desktopAccordionInitialized) return;

            const table = document.querySelector(config.seletores.orderReviewTable);
            const customerInfoWrapper = document.querySelector(config.seletores.customerInfoWrapper);
            if (!table || !customerInfoWrapper) return;

            let tableContainer = document.querySelector(config.seletores.customOrderSummaryContainer);
            if (!tableContainer) {
                tableContainer = document.createElement("div");
                tableContainer.className = config.seletores.customOrderSummaryContainer.substring(1);
                tableContainer.style.marginBottom = "20px";
                customerInfoWrapper.parentNode.insertBefore(tableContainer, customerInfoWrapper);
            }

            if (table.closest('.accordion-wrapper')) return;

            const accordionWrapper = document.createElement('div');
            accordionWrapper.className = 'accordion-wrapper';

            const totalAmountText = table.querySelector(config.seletores.tableTotalAmount)?.textContent.trim() || '';

            accordionWrapper.innerHTML = `
                <div class="accordion-header">
                    <div class="header-content">
                        <div class="header-left">
                            <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>
                            <span>Detalhes da compra</span>
                        </div>
                        <div class="header-right">
                            <span class="total-amount">${totalAmountText}</span>
                            <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </div>
                    </div>
                </div>
                <div class="accordion-content"></div>
            `;

            const accordionContent = accordionWrapper.querySelector('.accordion-content');
            accordionContent.appendChild(table);
            tableContainer.appendChild(accordionWrapper);

            accordionWrapper.querySelector('.accordion-header').addEventListener('click', () => {
                accordionWrapper.classList.toggle('active');
            });

            desktopAccordionInitialized = true;
            setupMutationObserver();
        }

        function teardownDesktopLayout() {
            if (!desktopAccordionInitialized) return;
            const accordionWrapper = document.querySelector(`${config.seletores.customOrderSummaryContainer} .accordion-wrapper`);
            const container = document.querySelector(config.seletores.customOrderSummaryContainer);
            if (accordionWrapper && container) {
                const originalTable = accordionWrapper.querySelector('.accordion-content > .shop_table');
                if (originalTable) {
                    container.appendChild(originalTable);
                }
                accordionWrapper.remove();
            }
            if (currentObserver) currentObserver.disconnect();
            desktopAccordionInitialized = false;
        }

        function handleResize() {
            const isDesktop = window.innerWidth > config.desktopBreakpoint;
            if (isDesktop && !desktopAccordionInitialized) {
                initializeDesktopLayout();
            } else if (!isDesktop && desktopAccordionInitialized) {
                teardownDesktopLayout();
            }
        }

        function init() {
            handleResize(); // Executa na carga inicial
            window.addEventListener('resize', () => {
                // Debounce para evitar execuções excessivas
                let timer;
                clearTimeout(timer);
                timer = setTimeout(handleResize, config.debounceDelay);
            });
        }

        return { init };
    })();

    /**
     * ====================================================================
     * MÓDULO DE LIMPEZA DO ORDER BUMP
     * ====================================================================
     */
    const OrderBump = (() => {
        function limparOrderBump() {
            const headers = document.querySelectorAll(config.seletores.orderBumpHeader);
            if (headers.length > 1) {
                for (let i = 1; i < headers.length; i++) {
                    headers[i].remove();
                }
            }

            if (headers.length === 0) {
                criarNovoHeader();
            }

            const ofertaOriginal = document.querySelector(config.seletores.orderBumpOffer);
            if (ofertaOriginal) {
                ofertaOriginal.style.display = 'none';
            }
        }

        function criarNovoHeader() {
            const ofertaDiv = document.querySelector(config.seletores.orderBumpOffer);
            const precoSpan = document.querySelector(config.seletores.orderBumpPrice);
            const titulo = ofertaDiv?.querySelector(config.seletores.orderBumpHighlight);

            if (!ofertaDiv || !precoSpan || !titulo) return;

            const header = document.createElement('div');
            header.className = config.seletores.orderBumpHeader.substring(1);
            header.appendChild(titulo.cloneNode(true));
            header.appendChild(precoSpan.cloneNode(true));
            ofertaDiv.parentNode.insertBefore(header, ofertaDiv);
        }

        function init() {
            // Execução robusta para garantir a limpeza
            setInterval(limparOrderBump, 750);
            document.addEventListener('click', () => setTimeout(limparOrderBump, 150));
            const observer = new MutationObserver(limparOrderBump);
            observer.observe(document.body, { childList: true, subtree: true });
        }

        return { init };
    })();

    /**
     * ====================================================================
     * INICIALIZAÇÃO GERAL
     * ====================================================================
     */
    Formulario.init();
    AcordeaoResumo.init();
    OrderBump.init();

});
