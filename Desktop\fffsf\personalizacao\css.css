li.wc_payment_method.payment_method_woo-mercado-pago-custom label {
    display: none !important;
}
@media only screen and (min-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 0px 24px 0 24px !important;
        min-height: 225px !important;
    }
}


.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {
    font-weight: 500;
    display: inline-block;
    width: calc(100% - 10%);
    color: var(--wcf-payment-section-label-color);
    font-size: 15px;
    display: none !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {

    min-height: 0px !important; 
 
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:last-child {

    border-top: none !important;
}


/* Estilo Moderno e Minimalista para Página de Pagamento PIX */

@media (min-width: 768px) {
img.mp-pix-template-image {
    width: 36% !important;
}
}



.mp-pix-template-image {
   
 padding-bottom:24px !important;
   
}




/* Container principal */
.mp-details-pix {
  max-width: 500px !important;
  margin: 0 auto !important;
  background-color: #ffffff !important;
  border-radius: 16px !important;

  overflow: hidden !important;
  font-family: 'Poppins', sans-serif !important;
}

/* Layout da seção PIX */
.mp-row-checkout-pix {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 16px 20px !important;
}

/* Esconder elementos desnecessários */
.mp-col-md-4, 
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description {
  display: none !important;
}

/* Ajustar coluna do QR code para ocupar toda largura e centralizar */
.mp-col-md-8 {
  width: 100% !important;
  padding: 0 !important;
  background-color: transparent !important;
  text-align: center !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

h3#billing_fields_heading {
    padding-bottom: 12px;
}

/* Título principal */
.mp-details-title {
  font-size: 22px !important;
  font-weight: 600 !important;
  color: #333333 !important;
  text-align: center !important;
  margin-bottom: 25px !important;
}

/* Valor a pagar */
.mp-details-pix-amount {
  margin-bottom: 30px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-qr {
  font-size: 16px !important;
  color: #666666 !important;
  display: block !important;
  margin-bottom: 8px !important;
  text-align: center !important;
}

.mp-details-pix-qr-value {
  font-size: 28px !important;
  font-weight: 700 !important;
  color: #333333 !important;
  text-align: center !important;
}

/* Título do QR code */
.mp-details-pix-qr-title {
  font-size: 16px !important;
  color: #666666 !important;
  margin-bottom: 20px !important;
  text-align: center !important;
  width: 100% !important;
}

.mp-details-pix-button:hover {
  background-color: #28a745 !important;
}
/* QR Code - Centralização corrigida */
.mp-details-pix-qr-img {
  width: 200px !important;
  height: 200px !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
}

/* Container do código PIX */
.mp-details-pix-container {
  width: 100% !important;
  max-width: 350px !important;
  margin: 0 auto !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
}

/* Container do input e botão */
.mp-row-checkout-pix-container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  width: 100% !important;
}

/* CORRIGIDO*/

/* Input do código PIX */
#mp-qr-code {
  width: 100% !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin-bottom: 15px !important;
  text-align: center !important;
}

/* Botão de copiar */
.mp-details-pix-button {
  width: 100% !important;
  padding: 14px 20px !important;
  background-color: #32CD32 !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
/* Adicionar ícone de cópia ao botão */
.mp-details-pix-button::before {
  content: "" !important;
  display: inline-block !important;
  width: 18px !important;
  height: 18px !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/%3E%3C/svg%3E") !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  margin-right: 8px !important;
}

/* Tempo restante */
.mp-row-checkout-pix::after {
  content: "Tempo restante: 30 minutos" !important;
  display: block !important;
  margin-top: 0px !important;
  font-size: 14px !important;
  color: #666666 !important;
  text-align: center !important;
  width: 100% !important;
}






/* Melhorias para a tabela de detalhes do pedido (imagem 2) */
.woocommerce-order-overview {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: space-between !important;
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 0 0 30px 0 !important;
  border: none !important;

}

.woocommerce-order-overview li {
  flex: 1 1 45% !important;
  margin: 5px 0 !important;
  padding: 8px !important;
  border: none !important;
  font-size: 14px !important;
  color: #666666 !important;
  display: flex !important;
  flex-direction: column !important;
}

.woocommerce-order-overview li strong {
  font-size: 16px !important;
  color: #333333 !important;
  font-weight: 600 !important;
  margin-top: 4px !important;
}

/* Melhorias para a área de status do pagamento (imagem 3) */
#transaction-status-message {
  background-color: #f8f9fa !important;

  padding: 15px !important;
  margin: 20px auto !important;
  max-width: 400px !important;

}
/* CORRIGIDO*/

#transaction-status-message div {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#transaction-status-message img {
  width: 24px !important;
  height: 24px !important;
  margin-right: 10px !important;
}

#transaction-status-message p {
  margin: 0 !important;
  color: #666666 !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}
.
@media (max-width: 480px) {
mp-row-checkout-pix {
    padding: 0px !important;
}
}
/* Responsividade para dispositivos móveis */
@media (max-width: 480px) {
.mp-details-pix {
    padding: 0px !important;
} 
}
    max-width: 100% !important;
    border-radius: 12px !important;
  }
  
  .mp-row-checkout-pix {
    padding: 25px 15px !important;
  }
@media (max-width: 767.98px) {
    .mp-pix-right {
       
        margin-bottom: 0px !important;
    }
}

  
  .mp-details-pix-qr-value {
    font-size: 24px !important;
  }
  
  .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  .woocommerce-order-overview li {
    flex: 1 1 100% !important;
  }
}

/* Estilo específico para centralizar o código PIX abaixo do QR code */
.mp-details-pix-container p,
.mp-pix-image-qr-code p,
#mp-qr-code,
.mp-details-pix-qr-code p {
  text-align: center !important;
  width: 100% !important;
  display: block !important;
  margin: 0 auto 15px !important;
}

/* Ajuste para o input que contém o código */
#mp-qr-code {
  width: 100% !important;
  max-width: 350px !important;
  padding: 12px 15px !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  color: #666666 !important;
  background-color: #f9f9f9 !important;
  margin: 0 auto 15px !important;
  text-align: center !important;
  display: block !important;
}


.mp-details-pix {
    display: flex !important;
    justify-content: center !important;
    text-align: center !important;
}
.mp-col-md-8.mp-text-center.mp-pix-right {
    border: none !important;
}
ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
    display: none !important;
}
p.mp-details-title {
    display: none !important;
}

/* CORRIGIDO*/
/* Container principal do PIX */
.mp-details-pix {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 100%;
    max-width: 1100px;
    margin: 0 auto;
    padding: 10px;
}

/* Linha de checkout do PIX */
.mp-row-checkout-pix {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Colunas */
.mp-col-md-4,
.mp-col-md-8 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 550px;
    margin: 0 auto;
    padding: 5px;
}

/* Container do código PIX */
.mp-details-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    text-align: center;
    margin: 0;
    padding: 5px 0;
}

/* Linha do container de checkout PIX */
.mp-row-checkout-pix-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
}

/* Input do código PIX */
#mp-qr-code {
    width: 100%;
    max-width: 400px;
    text-align: center;
    margin: 5px auto;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f8f8f8;
    font-size: 13px;
    color: #333;
}

/* Botão de copiar código */
.mp-details-pix-button {
    width: 100%;
    max-width: 400px;
    margin: 5px auto;
    padding: 8px;
    background-color: #32CD32;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 13px;
    display: block;
}

/* Imagem do QR code */
.mp-details-pix-qr-img {
    display: block;
    margin: 5px auto;
    max-width: 200px;
}


/* CORRIGIDO*/

/* Textos e títulos */
.mp-details-pix-title,
.mp-details-pix-qr-title,
.mp-details-pix-qr-subtitle,
.mp-details-pix-qr-description,
.mp-details-pix-amount,
.mp-details-pix p {
    text-align: center;
    width: 100%;
    margin: 5px auto;
    padding: 0;
    font-size: 14px;
}

/* Lista de passos */
.mp-steps-congrats {
    padding-left: 0;
    margin: 5px 0;
}

.mp-details-list {
    margin-bottom: 5px;
    padding: 0;
}

.mp-details-pix-number-p {
    margin: 0 0 2px 0;
}

.mp-details-list-description {
    margin: 0;
    font-size: 13px;
}

/* Valor a pagar */
.mp-details-pix-amount {
    margin: 5px 0;
}

.mp-details-pix-qr,
.mp-details-pix-qr-value {
    margin: 0;
    padding: 0;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    .mp-row-checkout-pix {
        flex-direction: column-reverse;
    }
    
    .mp-col-md-4,
    .mp-col-md-8 {
        width: 100%;
        max-width: 100%;
        padding: 5px;
    }
    
    #mp-qr-code,
    .mp-details-pix-button {
        max-width: 100%;
    }
    
    .mp-details-pix-img {
        max-width: 80px;
    }
}
.mp-row-checkout-pix-container {

  padding: 0px !important;
}
.mp-details-pix-amount {
   margin-bottom: 10px !important; 
  
}


/* CORRIGIDO*/

/* Para garantir que qualquer sombra nos contêineres seja removida */
.mp-details-pix {
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    background-color: transparent !important;
}
.elementor-366 .elementor-element.elementor-element {
 
    padding:0px !important;

}


.elementor-element.elementor-element-6a543efd.elementor-widget.elementor-widget-image {
    display: none !important;
}






















/* Estilo para aumentar o tamanho do QR code e torná-lo responsivo */
.woocommerce-order .mp-details-pix-qr-img {
  width: 280px !important;
  height: 280px !important;
  max-width: 90% !important;
  padding: 15px !important;
  background-color: #ffffff !important;
  border: 1px solid #e6e6e6 !important;
  border-radius: 12px !important;
  margin: 0 auto 30px !important;
  display: block !important;
  transition: transform 0.3s ease !important;
}

/* Efeito de hover para o QR code */
.woocommerce-order .mp-details-pix-qr-img:hover {
  transform: scale(1.05) !important;
}

/* Responsividade para diferentes tamanhos de tela */
@media (min-width: 768px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 250px !important;
    height: 250px !important;
  }
}

@media (min-width: 768px) {
.mp-details-pix-qr-value {
    font-size: 28px !important;
}
}
/* Para telas menores */
@media (max-width: 480px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 180px !important;
    height: 180px !important;
  }
  
  /* Ajustes para melhor visualização em telas pequenas */
  .woocommerce-order .mp-details-pix {
    padding: 10px 5px !important;
  }
  
  .woocommerce-order .mp-row-checkout-pix {
    padding: 15px 10px !important;
  }
}

/* Para telas muito pequenas */
@media (max-width: 320px) {
  .woocommerce-order .mp-details-pix-qr-img {
    width: 220px !important;
    height: 220px !important;
    padding: 10px !important;
  }
}

/* Melhorar a centralização do QR code */
.woocommerce-order .mp-col-md-8.mp-text-center.mp-pix-right {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

/* Ajustar o container principal para melhor responsividade */
.woocommerce-order .mp-details-pix {
  width: 100% !important;
  max-width: 600px !important;
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

@media only screen and (max-width: 768px) {
.header-right {
    display: flex !important;

    gap: 0px !important;

}
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
   
    margin: 22px 0 0px !important;
}

.wcf-bump-order-style-1 .wcf-bump-order-offer-content-left img {
    padding: 0 !important;
}
.wcf-bump-order-wrap .wcf-bump-order-field-wrap label {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    font-size: 1em !important;
    color: #495057 !important;
}
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}



.wcf-bump-order-header {
    padding: 0px !important;
}



/* --- Container dos Detalhes da Oferta (O Card Branco) --- */
/* Estilos Desktop */
.wcf-bump-order-wrap .wcf-content-container {
    display: flex !important;
    flex-direction: row !important; /* Lado a lado no Desktop */
    align-items: flex-start !important;
    gap: 20px !important;           /* Espaço entre imagem e texto no Desktop */
    background-color: #ffffff !important;
   
    border-radius: 8px !important;
    padding: 25px !important;      /* Padding Desktop */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}
/* --- Coluna Esquerda (Imagem) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-left.wcf-bump-order-image {
    flex-shrink: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
}

/* --- Imagem - Desktop --- */
.wcf-bump-order-wrap .wcf-image {
    display: block !important;
    /* Mantém estilos padrão ou anteriores da imagem no desktop */
    /* (Tamanho padrão do plugin ou tema) */
    max-width: 100%; /* Garante não estourar container */
    height: auto;    /* Mantem proporção */
    border-radius: 6px; /* Arredondamento padrão */
}

/* --- Coluna Direita (Bloco de Texto) - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer-content-right {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important; /* Empilha Título -> Preço -> Descrição */
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Área do Título ('Oferta Única') - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-offer {
    width: 100% !important;
    margin: 0 0 8px 0 !important;
    padding: 0 !important;
    order: 1 !important;            /* Título Primeiro */
    text-align: left !important;
}

.wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
    font-weight: 600 !important;

    color: #343a40 !important;
    display: block !important;
    line-height: 1.3 !important;
}

/* --- Container da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc {
    order: 2 !important;            /* Descrição (com preço) vem depois */
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    font-size: 0.95em !important;
    line-height: 1.6 !important;
    color: #6c757d !important;
}

/* --- Parágrafo DENTRO da Descrição - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p {
    margin: 0 !important;
    padding: 0 !important;
}

/* --- Span do Preço DENTRO do Parágrafo - Desktop --- */
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
    display: block !important;      /* Preço na linha de cima */
 
    color: #212529 !important;
    line-height: 1.2 !important;
    margin-bottom: 12px !important; /* Espaço abaixo */
}

/* Ocultar <br> extras */
.wcf-bump-order-wrap .wcf-bump-order-desc p br {
    display: none !important;
}
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + br,
.wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price + .nbsp {
    display: none !important;
}



}

/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 0px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}
@media (max-width: 600px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 15px 16px 0 16px !important;
        margin: 0px !important;
    }
}

@media only screen and (min-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@m

@media only screen and (max-width: 768px) {
    @media only screen and (min-width: 768px) {
        .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
            padding: 16px 24px 0 24px !important;
            margin: 0px !important;
        }
    }
}
@media only screen and (max-width: 768px) {
.mp-checkout-custom-card-form .mp-checkout-custom-card-row {

    padding-bottom: 16px !important;
}
}
@media only screen and (max-width: 768px) {
div#mp-card-holder-div {
	
    padding-bottom: 16px !important;
	
}
}


@media only screen and (max-width: 768px) {
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {

    padding: 12px !important;
}
}


/* Ajustes finos para telas MUITO pequenas (Ex: abaixo de 400px) */
@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-content-container {
        padding: 15px !important;
        gap: 12px !important;
    }
     .wcf-bump-order-wrap .wcf-image {
        width: 60px !important; /* Pode reduzir um pouco mais se necessário */
     }
     .wcf-bump-order-wrap .wcf-bump-order-bump-highlight {
        font-size: 1.05em !important;
     }
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 1.4em !important;
     }
     .wcf-bump-order-wrap .wcf-bump-order-desc p { /* Aplica ao texto da descrição no <p> */
        font-size: 0.85em !important;
     }
}


@media (max-width: 480px) {
.wcf-bump-order-field-wrap {
    padding: 14px !important;
}
}





/* CSS de suporte para o JavaScript */
.wcf-bump-order-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 10px !important;
}

.wcf-bump-order-header .wcf-bump-order-offer {
    display: block !important;
    margin: 0 !important;
}

.wcf-bump-order-header .wcf-normal-price {
    display: block !important;
    margin: 0 !important;
    font-weight: bold !important;
}

/* Ajustes para mobile */
@media only screen and (max-width: 767px) {
    .wcf-bump-order-header {
        padding: 0 5px !important;
    }
}

@media only screen and (max-width: 767px) {
span.wcf-normal-price {
    text-align: left !important;
}
}
@media only screen and (max-width: 767px) {
.wcf-bump-order-desc {
    text-align: left !important;
}
}


@media (max-width: 480px) {
    .wcf-bump-order-wrap .wcf-bump-order-desc p span.wcf-normal-price {
        font-size: 14px !important;
    }
}
@media (max-width: 480px) {
span.woocommerce-Price-amount.amount {
    font-size: 14px !important;
}
}


.wc_payment_method > input[type="radio"]:checked + label {
    background: #f9fafb !important;
    border-color: #3BAE7E !important;
    /* border-width: 2px !important; */
    border-style: solid !important;
    background-color: #e8f5e99e !important;
}























/* CSS para o aviso de pagamento seguro */
.secure-payment-notice {
    display: flex;
    align-items: center;
    color: #2ecc71;
    margin: 15px 0;
    font-size: 14px;
    line-height: 1.4;
}

.secure-payment-notice svg {
    margin-right: 8px;
    flex-shrink: 0;
}

.secure-payment-notice span {
    color: #2ecc71;
}

/* Versão mobile - ícone acima do texto centralizado */
@media only screen and (max-width: 767px) {
    .secure-payment-notice {
        flex-direction: column !important;
        text-align: center !important;
        justify-content: center !important;
    }
    
    .secure-payment-notice svg {
        margin-right: 0 !important;
        margin-bottom: 8px !important;
    }
    
    .secure-payment-notice span {
        text-align: center !important;
        width: 100% !important;
    }
}



li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    margin: 0px !important;
}

/* Estilos para a seção de pagamento */
#payment {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
}
@media (max-width: 600px) {
#payment {
  
    padding: 0px;
  
}
}


@media (max-width: 600px) {
.mp-checkout-custom-container {
    padding: 0px !important;
}
}
div#mp-checkout-custom-installments {
    padding: 0px !important;
}


@media (max-width: 600px) {
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
    padding: 15px 16px 0 16px !important;

}
}
.wcf-embed-checkout-form .woocommerce-checkout #payment {
   
    border-radius: 8px !important;
 
}


/* --- Reset Básico para o Container LI --- */
li.wc_payment_method.payment_method_woo-mercado-pago-pix {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 0 20px 0 !important; /* Aumentar margem inferior */
    list-style: none !important;
}

/* --- Esconder o Radio Button Original --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 1px !important;
    height: 1px !important;
}

/* --- Estilo do Label (Botão de Seleção PIX) --- */
li.payment_method_woo-mercado-pago-pix label {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    padding: 14px 22px !important; /* Levemente mais padding */
    border: 1px solid #dee2e6 !important; /* Borda cinza um pouco mais visível */
    border-radius: 8px !important;
    cursor: pointer;
    background-color: #ffffff !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
    color: #495057 !important;
    transition: all 0.2s ease-in-out !important;
    box-sizing: border-box !important;
    margin-bottom: 15px !important; /* Espaço antes da caixa */
}

/* --- Estilo do Ícone Pequeno Dentro do Label (Mercado Pago) --- */
li.payment_method_woo-mercado-pago-pix label img {
    width: 22px !important; /* Ligeiramente maior */
    height: 22px !important;
    margin-right: 12px !important;
    flex-shrink: 0;
}

/* --- Estilo do Label QUANDO SELECIONADO (Mais Refinado) --- */
li.payment_method_woo-mercado-pago-pix input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
                  background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important;
   
}

/* --- Caixa de Detalhes do Pagamento (Com mais presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix {
    padding: 35px 25px !important; /* Mais padding interno */
    background-color: #f8f9fa !important; /* Fundo cinza claro */
    border: 1px solid #e9ecef !important; /* Borda sutil um pouco mais escura que o fundo */
    border-radius: 8px !important;
    margin-top: 0 !important;
    text-align: center !important;
    clear: both !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important; /* Sombra externa muito sutil */
    position: relative; /* Para posicionamento de elementos internos se necessário */
}

/* --- Container Interno (Flexbox para alinhar conteúdo) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-container {
    border: none !important;
    padding: 0 !important;
    background-color: transparent !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

/* --- SEU SVG Principal --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-image {
    content: url('data:image/svg+xml;utf8,<svg width="80" height="80" viewBox="0 0 193 193" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M143.749 141.252C140.491 141.259 137.264 140.621 134.253 139.375C131.243 138.128 128.509 136.298 126.21 133.99L100.879 108.659C99.982 107.807 98.7923 107.332 97.5555 107.332C96.3186 107.332 95.129 107.807 94.2323 108.659L68.8045 134.087C66.5059 136.396 63.7724 138.227 60.7619 139.473C57.7515 140.72 54.5239 141.357 51.2656 141.348H46.2718L78.358 173.435C88.3699 183.446 104.618 183.446 114.63 173.435L146.801 141.252H143.749ZM51.2656 51.6516C57.9 51.6516 64.1243 54.233 68.8045 58.9132L94.2323 84.341C94.6692 84.7787 95.1882 85.1259 95.7594 85.3628C96.3307 85.5997 96.9431 85.7216 97.5615 85.7216C98.18 85.7216 98.7923 85.5997 99.3636 85.3628C99.9349 85.1259 100.454 84.7787 100.891 84.341L126.222 59.0097C128.519 56.702 131.251 54.872 134.259 53.6257C137.267 52.3793 140.493 51.7412 143.749 51.7481H146.801L114.63 19.5774C109.819 14.7696 103.296 12.0689 96.494 12.0689C89.6924 12.0689 83.1691 14.7696 78.358 19.5774L46.2718 51.6637L51.2656 51.6516Z" fill="%2330BEAF"/><path d="M173.423 78.358L153.978 58.9132C153.541 59.0925 153.075 59.1867 152.603 59.1907H143.761C139.189 59.1907 134.714 61.0483 131.493 64.281L106.162 89.6123C105.036 90.7447 103.696 91.6434 102.221 92.2566C100.746 92.8698 99.1649 93.1855 97.5675 93.1855C95.9702 93.1855 94.3886 92.8698 92.9137 92.2566C91.4387 91.6434 90.0995 90.7447 88.973 89.6123L63.5453 64.1725C60.2829 60.9236 55.8698 59.0941 51.2656 59.0821H40.4094C39.9593 59.0785 39.5137 58.9927 39.0946 58.8288L19.5774 78.358C9.56557 88.3699 9.56557 104.618 19.5774 114.642L39.0946 134.159C39.5091 133.992 39.9506 133.902 40.3973 133.894H51.2656C55.8494 133.894 60.3125 132.048 63.5453 128.815L88.9609 103.376C91.28 101.168 94.3594 99.9361 97.5615 99.9361C100.764 99.9361 103.843 101.168 106.162 103.376L131.493 128.707C134.714 131.94 139.189 133.785 143.761 133.785H152.603C153.085 133.785 153.556 133.906 153.978 134.075L173.423 114.63C183.434 104.618 183.434 88.3699 173.423 78.358Z" fill="%2330BEAF"/></svg>') !important;
    width: 65px !important; /* Tamanho um pouco menor para mais respiro */
    height: 65px !important;
    display: block !important;
    margin: 0 auto 25px auto !important; /* Garante centralização e margem inferior */
    padding: 0 !important;
    background: none !important;
    border: none !important;
}

/* --- Estilo do Título Principal na Caixa PIX (Mais Presença) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-title {
    font-size: 1.25em !important; /* Um pouco maior */
    font-weight: 600 !important;
    color: #343a40 !important; /* Cinza mais escuro */
    margin: 0 0 12px 0 !important; /* Mais espaço abaixo */
    line-height: 1.4 !important;
}


        li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
            border-color: #3BAE7E !important;
            /* border-width: 2px !important; */
            border-style: solid !important;
            background-color: #e8f5e99e !important;
            /* box-shadow: 0 0 0 2px rgba(59, 174, 126, 0.25) !important; */
        }
  
/* --- Estilo do Subtítulo/Descrição na Caixa PIX (Clareza) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-pix-template-subtitle {
    font-size: 1em !important; /* Tamanho padrão para boa leitura */
    color: #495057 !important;
    line-height: 1.6 !important;
    margin: 0 auto !important;
    max-width: 90% !important;
}

/* --- Ocultar Termos e Condições (Mantido opcional) --- */
.payment_box.payment_method_woo-mercado-pago-pix .mp-checkout-pix-terms-and-conditions {
    display: none !important;
}

.secure-payment-notice {
  display: flex;          /* Alinha o ícone e o texto na mesma linha */
  align-items: center;    /* Centraliza verticalmente o ícone e o texto */
  color: #3BAE7E;       /* Define a cor para o texto e para o SVG (via currentColor) */
  font-size: 0.9em;       /* Tamanho de fonte um pouco menor para aviso */
  margin-top: 15px;       /* Adiciona um espaço acima do aviso (ajuste conforme necessário) */
  gap: 8px;               /* Espaço entre o ícone e o texto (alternativa a margin) */
}

/* Opcional: Se precisar ajustar o tamanho do ícone especificamente */
.secure-payment-notice svg {
  width: 16px;            /* Garante o tamanho */
  height: 16px;
  flex-shrink: 0;       /* Impede que o ícone encolha se o espaço for limitado */
}

/* Não é estritamente necessário se a cor for definida no container, mas para garantir: */
.secure-payment-notice span {
  line-height: 1.4;     /* Melhora a leitura se o texto quebrar linha */
}


/* Garante que os itens fiquem empilhados verticalmente (se for flex) */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper {
  display: block !important; /* Tente block ou flex */
  /* Se usar flex, garanta a direção: */
  /* flex-direction: column !important; */
}

/* Reseta a ordem de TODOS os campos dentro do wrapper */
.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > p.form-row {
   order: initial !important; /* Tenta resetar a ordem */
   position: static !important; /* Tenta resetar posição */
   float: none !important; /* Tenta resetar float */
   margin-top: initial !important; /* Tenta resetar margem */
}

/* OU, você pode definir a ordem explicitamente (menos recomendado, mas funciona) */
/* Dê números sequenciais para a ordem visual desejada */
/* #billing_first_name_field { order: 1 !important; } */
/* #billing_email_field { order: 2 !important; } */
/* #billing_cpf_cnpj_field { order: 3 !important; } */
/* #billing_cellphone_field { order: 4 !important; } */
/* Continue para outros campos visíveis se necessário... */


ul.wc_payment_methods.payment_methods.methods {
    border-radius: 8px !important;
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: 1px solid #bdbdbd5c !important;
}

span.wcf-field-required-error {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

@media only screen and (max-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce {
        padding: 0 0px !important;
    }
}

div#mp-doc-div {
    position: absolute !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    /* Preserva a funcionalidade, mas oculta visualmente */
}

.wcf-customer-info-main-wrapper {
    border: 1px solid #73737338 !important;
    background: #fff;
    padding-top: 0px !important; 
    padding-bottom: 28px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border: none !important;
}

@media only screen and (min-width: 768px) {
    div#mp-doc-div {
        margin-top: -22px;
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
	
    /* Mantemos posição absoluta e zero opacidade em vez de display:none */
    p#mp-security-code-info {
        position: absolute !important;
        opacity: 0 !important;
        pointer-events: none !important;
        height: 0 !important;
        overflow: hidden !important;
        visibility: hidden !important;
        /* Preserva a funcionalidade, mas oculta visualmente */
    }
}

.mp-checkout-custom-container {
    padding: 0px !important; 
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
    padding: 14px 16px 0 16px !important;
}

input#billing_first_name {
    color: black;
}

input#billing_cpf_cnpj {
    color: black;
}

input#billing_cellphone {
    color: black;
}

input#billing_email {
    color: black;
}

@media only screen and (min-width: 768px) {
    .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
        padding: 0px 24px 0 24px !important;
    }

    .mp-checkout-custom-card-form .mp-checkout-custom-card-row {
        padding-bottom: 14px !important;
    }

    li.payment_method_woo-mercado-pago-custom input[type="radio"]:checked + label {
        border-color: #3BAE7E !important;
        border-style: solid !important;
        background-color: #e8f5e99e !important;
    }
}

table.shop_table.woocommerce-checkout-review-order-table {
    background-color: #f1f9f196 !important;
    border-radius: 6px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address, 
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
    margin: 22px 0 0px !important;
}

@media (max-width: 768px) {
    .accordion-wrapper.active .accordion-header {
        border-bottom-color: transparent !important; 
    }

    .accordion-header {
        padding: 0px !important;
    }
}

/* Estilo adicional para garantir que os inputs do Mercado Pago fiquem visíveis */
.mp-input-table-container,
.mp-input-table-list,
.mp-input-table-bank-interest-container {
    clip-path: none !important;
    clip: auto !important;
    /* Garante que permaneçam visíveis para o JavaScript */
}

 @media (min-width: 728px) {
.mp-pix-template-container {
    padding-bottom: 16px !important;
    padding-top: 0px !important;
}
}

.elementor-364 .elementor-element.elementor-element-2faaddc4 .wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
    border: none !important;
}

/* Reset de estilos do CartFlows */
.wcf-collapsed-order-review-section {
display: none !important;
}

.accordion-wrapper {
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
background: white;
border-radius: 8px;
box-shadow: 0 1px 3px rgba(0,0,0,0.1);
margin-bottom: 20px;
padding: 16px;
display: block !important;
width: 100% !important;
box-sizing: border-box;
}

/* Estilos do cabeçalho */
.header-left span {
color: black;
font-weight: 600;
}

.header-right span.total-amount {
color: black;
}

.accordion-header {
display: flex;
justify-content: space-between;
align-items: center;
cursor: pointer;
padding: 8px 0;
width: 100%;
}

.header-content {
display: flex;
align-items: center;
justify-content: space-between;
width: 100%;
}

.header-left {
display: flex;
align-items: center;
gap:12px !important;
}

.header-right {
display: flex;
align-items: center;
gap: 12px;
}

/* Ícones */
.cart-icon {
stroke: currentColor;
}
.chevron-icon {
transition: transform 0.3s ease;
}

/* Conteúdo do acordeon */
.accordion-content {
display: none;
padding-top: 16px;
width: 100%;
}

.accordion-wrapper.active .accordion-content {
display: block !important;
}

.accordion-wrapper.active .chevron-icon {
transform: rotate(180deg);
}

/* Estilos da tabela */
.shop_table {
width: 100%;
border-collapse: collapse;
margin-top: 10px;
}
.cartflows_table thead {
background-color: #f8f9fa;
}

.cartflows_table th,
.cartflows_table td {
padding: 12px;
text-align: left;
}
@media screen and (max-width: 769px) {
.wcf-product-image {
display: flex;
align-items: center;
gap: 0px !important;
}
}

@media screen and (min-width: 769px) {
table.shop_table.woocommerce-checkout-review-order-table {
    /* padding: 50px !important; */
    padding-right: 20px !important;
    padding-left: 20px !important;
}
}
@media screen and (max-width: 769px) {
.wcf-embed-checkout-form table.shop_table thead tr th:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tbody tr td:nth-child( 2 ), .wcf-embed-checkout-form table.shop_table tfoot tr td:nth-child( 2 ) {

    padding-left: 30px !important;
}
}
.wcf-product-thumbnail img {
width: 48px;
height: 48px;
object-fit: cover;
border-radius: 4px;
}
@media (max-width: 768px) {
.accordion-wrapper.mobile-only {
    margin: 0 !important;
}
}
@media (max-width: 768px) {
table.shop_table.woocommerce-checkout-review-order-table.cartflows_table {
    background: #f9fafb;
    border-radius: 4px !important;
}
}

@media (max-width: 768px) {
.wcf-product-thumbnail img {
    width: 48px !important;
    height: 48px !important;
  
}
}

@media (max-width: 768px) {
h3#billing_fields_heading {
   
    margin-top: -0.1px !important;
}
}


li.payment_method_woo-mercado-pago-pix label {
   
    color: #000000 !important;
  
}


.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
    border-top: none !important;
  
}

p.mp-pix-template-title {
    font-weight: 600 !important;
}
@media (max-width: 768px) {
/* Garantir visibilidade do wrapper */
#order_review,
.woocommerce-checkout-review-order-table {
display: block !important;
width: 100% !important;
}
}

/* Ajustes para o container principal */
form.checkout {
width: 100% !important;
max-width: 100% !important;
}
pix-template,
.mp-pix-template-container,
.mp-pix-template-title,
.mp-pix-template-subtitle {
    font-family: "Inter", sans-serif !important;
}