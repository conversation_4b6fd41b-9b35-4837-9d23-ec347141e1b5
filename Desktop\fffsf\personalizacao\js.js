// Aguarda o DOM estar completamente carregado
document.addEventListener('DOMContentLoaded', function() {
    // Seleciona o campo de email pelo ID
    const emailField = document.getElementById('billing_email');
    
    // Verifica se o elemento existe antes de modificar
    if (emailField) {
        // Altera o placeholder para o novo texto desejado
        emailField.placeholder = 'Digite seu melhor e-mail';
        
        // Adiciona um evento de foco para melhorar a experiência do usuário
        emailField.addEventListener('focus', function() {
            this.placeholder = '';
        });
        
        // Adiciona um evento de perda de foco
        emailField.addEventListener('blur', function() {
            if (!this.value) {
                this.placeholder = 'Digite seu melhor e-mail';
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Encontra o elemento pelo ID
    const paymentHeading = document.getElementById('payment_options_heading');
    
    // Verifica se o elemento existe
    if (paymentHeading) {
        // Altera o texto para "Pagamento"
        paymentHeading.textContent = "Pagamento";
    }
});


document.addEventListener("DOMContentLoaded", function () {

    let desktopAccordionInitialized = false; // Flag to track if desktop setup is active
    let currentObserver = null; // Variable to hold the MutationObserver instance

    // --- Function to Update Accordion Header Total ---
    function updateAccordionHeaderTotal() {
        // Target the total *within* the table, wherever it is (likely inside accordion content)
        const totalElement = document.querySelector('.custom-order-summary-container .shop_table.woocommerce-checkout-review-order-table .order-total .woocommerce-Price-amount');
        // Target the display span in the accordion header
        const headerTotalSpan = document.querySelector('.custom-order-summary-container .accordion-header .total-amount');

        if (totalElement && headerTotalSpan) {
            headerTotalSpan.textContent = totalElement.textContent;
            // console.log('Header total updated:', totalElement.textContent); // For debugging
        } else {
            // console.log('Update total failed: Element(s) not found.', totalElement, headerTotalSpan); // For debugging
        }
    }

    // --- Function to Setup Mutation Observer ---
    function setupMutationObserver() {
        if (currentObserver) {
            currentObserver.disconnect(); // Disconnect previous if exists
        }

        // Observe the container where updates are expected
        const containerToObserve = document.querySelector('.custom-order-summary-container');

        if (containerToObserve) {
            currentObserver = new MutationObserver(function(mutations) {
                // Check if relevant nodes changed (e.g., price amount)
                 let relevantChange = false;
                 mutations.forEach(mutation => {
                     // Simple check: look for changes within the table or if nodes containing prices were added/removed
                     if (mutation.target.closest('.shop_table') || mutation.target.querySelector('.woocommerce-Price-amount')) {
                         relevantChange = true;
                     }
                      // More robust: check specifically if '.order-total .woocommerce-Price-amount' text changed
                     if (mutation.type === 'characterData' && mutation.target.parentElement?.closest('.order-total')) {
                         relevantChange = true;
                     }
                 });

                 if (relevantChange) {
                    // Use setTimeout to allow WC AJAX potentially finish updates before grabbing total
                    setTimeout(updateAccordionHeaderTotal, 150);
                 }
            });

            currentObserver.observe(containerToObserve, {
                subtree: true,
                childList: true,
                characterData: true,
                attributes: true, // Observe attribute changes too, might be needed for some AJAX updates
                attributeFilter: ['class', 'style'] // Optional: filter specific attributes if needed
            });
            // console.log('Observer attached to .custom-order-summary-container'); // For debugging
        } else {
            // console.log('Observer setup failed: .custom-order-summary-container not found'); // For debugging
        }
    }

    // --- Function to Initialize Desktop Layout (Move Table + Add Accordion) ---
    function initializeDesktopLayout() {
        if (desktopAccordionInitialized) {
            // console.log('Desktop init aborted: already initialized.'); // For debugging
            return; // Prevent re-initialization
        }

        // === Part 1: Find, Clean Up, and Move the Table ===
        const tables = document.querySelectorAll(".shop_table.woocommerce-checkout-review-order-table");
        const customerInfoWrapper = document.querySelector(".wcf-customer-info-main-wrapper");

        if (tables.length === 0 || !customerInfoWrapper) {
             // console.log('Desktop init aborted: missing table or customer info wrapper.'); // For debugging
            return; // Essential elements missing
        }

        // Remove duplicates (keep the first one)
        for (let i = tables.length - 1; i > 0; i--) { // Iterate backwards for safe removal
             if (tables[i].parentNode) {
                tables[i].parentNode.removeChild(tables[i]);
                // console.log('Removed duplicate table:', i); // For debugging
             }
        }

        const tableToModify = tables[0]; // The single table we will work with

        // Create or find the custom container
        let tableContainer = document.querySelector(".custom-order-summary-container");
        if (!tableContainer) {
            tableContainer = document.createElement("div");
            tableContainer.classList.add("custom-order-summary-container");
            tableContainer.style.marginBottom = "20px";
            tableContainer.style.width = "100%";
            tableContainer.style.boxSizing = "border-box";
            customerInfoWrapper.parentNode.insertBefore(tableContainer, customerInfoWrapper);
            // console.log('.custom-order-summary-container created.'); // For debugging
        }

        // Ensure the table is inside the container
        if (tableToModify.parentNode !== tableContainer) {
            tableContainer.appendChild(tableToModify);
            // console.log('Table moved into .custom-order-summary-container.'); // For debugging
        }

        // === Part 2: Wrap the Table in an Accordion ===
        // Check if it's already wrapped (e.g., if resize happens fast)
        if (tableToModify.closest('.accordion-wrapper')) {
             // console.log('Accordion init aborted: table already wrapped.'); // For debugging
            return;
        }

        const accordionWrapper = document.createElement('div');
        accordionWrapper.className = 'accordion-wrapper';

        // Get total amount *from the table element*
        const totalElement = tableToModify.querySelector('.order-total .woocommerce-Price-amount');
        const totalAmountText = totalElement ? totalElement.textContent.trim() : ''; // Use trim()

        const accordionHeader = document.createElement('div');
        accordionHeader.className = 'accordion-header';
        accordionHeader.innerHTML = `
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%; cursor: pointer;">
                <div class="header-left" style="display: flex; align-items: center;">
                    <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; margin-right: 8px;">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                    <span>Detalhes da compra</span>
                </div>
                <div class="header-right" style="display: flex; align-items: center;">
                    <span class="total-amount" style="margin-right: 8px;">${totalAmountText}</span>
                    <svg class="chevron-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="vertical-align: middle; transition: transform 0.3s ease;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </div>
            </div>
        `;

        const accordionContent = document.createElement('div');
        accordionContent.className = 'accordion-content';
        // Style for initial state (hidden) - relies on CSS `.active` class to override
        accordionContent.style.display = 'none';
        accordionContent.style.overflow = 'hidden'; // Helps with transitions

        // Move the actual table element into the content div
        accordionContent.appendChild(tableToModify);

        // Assemble the wrapper
        accordionWrapper.appendChild(accordionHeader);
        accordionWrapper.appendChild(accordionContent);

        // Replace the table's original position (now empty) within the container with the new wrapper
        // Since tableToModify was moved into accordionContent, its original spot is effectively empty.
        // We append the wrapper to the container.
        tableContainer.appendChild(accordionWrapper);
        // console.log('Accordion wrapper added, containing the table.'); // For debugging


        // === Part 3: Add Click Listener ===
        // Add listener AFTER the header is part of the wrapper and potentially in the DOM
        const headerElement = accordionWrapper.querySelector('.accordion-header'); // Target specifically
        if (headerElement) {
             headerElement.addEventListener('click', function() {
                 // console.log('Accordion header clicked!'); // For debugging
                 accordionWrapper.classList.toggle('active');

                 // Toggle content visibility directly and chevron rotation
                 const content = accordionWrapper.querySelector('.accordion-content');
                 const chevron = headerElement.querySelector('.chevron-icon');
                 if (accordionWrapper.classList.contains('active')) {
                     content.style.display = 'block'; // Or 'grid', 'flex', etc., depending on table display
                     if(chevron) chevron.style.transform = 'rotate(180deg)';
                     // Optional: Animate height using JS if CSS transitions aren't sufficient
                 } else {
                     content.style.display = 'none';
                      if(chevron) chevron.style.transform = 'rotate(0deg)';
                     // Optional: Animate height back to 0
                 }
             });
             // console.log('Click listener added to accordion header.'); // For debugging
        } else {
             // console.log('Error: Accordion header not found for adding click listener.'); // For debugging
        }


        desktopAccordionInitialized = true; // Mark as initialized

        // === Part 4: Setup Observer ===
        setupMutationObserver();
    }

    // --- Function to Teardown Desktop Layout (Remove Accordion) ---
    function teardownDesktopLayout() {
        if (!desktopAccordionInitialized) {
            // console.log('Desktop teardown aborted: not initialized.'); // For debugging
            return;
        }

        const accordionWrapper = document.querySelector('.custom-order-summary-container .accordion-wrapper');
        const container = document.querySelector('.custom-order-summary-container');

        if (accordionWrapper && container) {
            // Find the original table *inside* the accordion content
            const originalTable = accordionWrapper.querySelector('.accordion-content > .shop_table.woocommerce-checkout-review-order-table'); // More specific selector

            if (originalTable) {
                // Put the table back into the container directly
                container.appendChild(originalTable);
                 // Remove the now-empty accordion wrapper
                accordionWrapper.remove();
                // console.log('Accordion wrapper removed, table restored in container.'); // For debugging
            } else {
                 // Fallback if table not found inside: just remove wrapper
                 accordionWrapper.remove();
                 // console.log('Accordion wrapper removed (table not found inside during teardown).'); // For debugging
            }
        }

        // Disconnect the observer when leaving desktop view
        if (currentObserver) {
            currentObserver.disconnect();
            currentObserver = null;
            // console.log('Observer disconnected.'); // For debugging
        }

        desktopAccordionInitialized = false; // Mark as ready for re-initialization if needed
    }

    // --- Initial Check and Resize Handling ---
    let isCurrentlyDesktop = window.innerWidth > 768;

    // Run on initial load if desktop
    if (isCurrentlyDesktop) {
        initializeDesktopLayout();
    }

    // Debounce resize function to avoid excessive calls
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            const wasDesktop = isCurrentlyDesktop;
            isCurrentlyDesktop = window.innerWidth > 768;

            // console.log(`Resize detected. Was Desktop: ${wasDesktop}, Is Desktop: ${isCurrentlyDesktop}`); // For debugging

            if (isCurrentlyDesktop && !wasDesktop) {
                // Transitioned from Mobile to Desktop
                // console.log('Resize: Mobile -> Desktop. Initializing...'); // For debugging
                initializeDesktopLayout();
            } else if (!isCurrentlyDesktop && wasDesktop) {
                // Transitioned from Desktop to Mobile
                // console.log('Resize: Desktop -> Mobile. Tearing down...'); // For debugging
                teardownDesktopLayout();
            }
        }, 250); // 250ms delay
    });

}); // End DOMContentLoaded


// Script para remover todos os elementos duplicados no order bump
(function() {
    function limparOrderBump() {
        // Verificar se já existe algum header
        var headers = document.querySelectorAll('.wcf-bump-order-header');
        
        // Se existir mais de um header, manter apenas o primeiro
        if (headers.length > 1) {
            for (var i = 1; i < headers.length; i++) {
                if (headers[i].parentNode) {
                    headers[i].parentNode.removeChild(headers[i]);
                }
            }
        }
        
        // Se não existir nenhum header, criar um novo
        if (headers.length === 0) {
            criarNovoHeader();
        }
        
        // Esconder a oferta original
        var ofertaOriginal = document.querySelector('.wcf-bump-order-offer');
        if (ofertaOriginal) {
            ofertaOriginal.style.display = 'none';
        }
        
        // Remover todos os títulos duplicados fora do header
        var header = document.querySelector('.wcf-bump-order-header');
        if (header) {
            var tituloNoHeader = header.querySelector('.wcf-bump-order-bump-highlight');
            var todosTitulos = document.querySelectorAll('.wcf-bump-order-bump-highlight');
            
            for (var i = 0; i < todosTitulos.length; i++) {
                if (todosTitulos[i] !== tituloNoHeader && todosTitulos[i].closest('.wcf-bump-order-header') !== header) {
                    var elementoPai = todosTitulos[i].parentNode;
                    if (elementoPai && !elementoPai.classList.contains('wcf-bump-order-header')) {
                        elementoPai.style.display = 'none';
                    }
                }
            }
        }
        
        // Remover todos os preços duplicados
        var precoNoHeader = header ? header.querySelector('.wcf-normal-price') : null;
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        
        for (var i = 0; i < todosPrecos.length; i++) {
            if (todosPrecos[i] !== precoNoHeader) {
                if (todosPrecos[i].parentNode) {
                    todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
                }
            }
        }
    }
    
    function criarNovoHeader() {
        // Encontrar os elementos originais
        var ofertaDiv = document.querySelector('.wcf-bump-order-offer');
        var precoSpan = document.querySelector('.wcf-normal-price');
        
        if (!ofertaDiv || !precoSpan) return;
        
        // Criar o header
        var header = document.createElement('div');
        header.className = 'wcf-bump-order-header';
        header.style.display = 'flex';
        header.style.justifyContent = 'space-between';
        header.style.alignItems = 'center';
        header.style.marginBottom = '10px';
        header.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaDiv.querySelector('.wcf-bump-order-bump-highlight');
        if (!titulo) return;
        
        // Criar clones dos elementos
        var tituloClone = titulo.cloneNode(true);
        var precoClone = precoSpan.cloneNode(true);
        
        // Adicionar os elementos ao header
        header.appendChild(tituloClone);
        header.appendChild(precoClone);
        
        // Inserir o header antes da oferta
        ofertaDiv.parentNode.insertBefore(header, ofertaDiv);
    }
    
    // Executar a função imediatamente
    limparOrderBump();
    
    // Executar a cada 500ms para garantir que o layout permaneça correto
    setInterval(limparOrderBump, 500);
    
    // Executar após cliques
    document.addEventListener('click', function() {
        setTimeout(limparOrderBump, 100);
        setTimeout(limparOrderBump, 500);
    });
    
    // Executar após mudanças no DOM
    var observer = new MutationObserver(function() {
        limparOrderBump();
    });
    
    // Iniciar o observador
    if (document.body) {
        observer.observe(document.body, { childList: true, subtree: true });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }
})();


// Script para colocar o título e o preço na mesma linha e remover o preço duplicado
(function() {
    // Função para executar quando o DOM estiver carregado
    function ajustarOrderBump() {
        // Selecionar os elementos
        var ofertaUnica = document.querySelector('.wcf-bump-order-offer');
        var precoElement = document.querySelector('.wcf-normal-price');
        
        // Verificar se os elementos existem
        if (!ofertaUnica || !precoElement) {
            console.log("Elementos não encontrados, tentando novamente em 500ms");
            setTimeout(ajustarOrderBump, 500);
            return;
        }
        
        // Criar um novo container para ambos os elementos
        var novoContainer = document.createElement('div');
        novoContainer.className = 'wcf-bump-order-header';
        novoContainer.style.display = 'flex';
        novoContainer.style.justifyContent = 'center';
        novoContainer.style.alignItems = 'center';
        novoContainer.style.marginBottom = '10px';
        novoContainer.style.width = '100%';
        
        // Pegar o título
        var titulo = ofertaUnica.querySelector('.wcf-bump-order-bump-highlight');
        
        // Criar um clone do título para o novo container
        var tituloClone = titulo.cloneNode(true);
        tituloClone.style.marginRight = '15px';
        
        // Criar um clone do preço para o novo container
        var precoClone = precoElement.cloneNode(true);
        
        // Adicionar os elementos ao novo container
        novoContainer.appendChild(tituloClone);
        novoContainer.appendChild(precoClone);
        
        // Encontrar o elemento pai onde inserir o novo container
        var parentElement = ofertaUnica.parentNode;
        
        // Inserir o novo container antes do elemento original
        parentElement.insertBefore(novoContainer, ofertaUnica);
        
        // Esconder os elementos originais
        ofertaUnica.style.display = 'none';
        
        // Encontrar e esconder o elemento <br> se existir
        var descElement = document.querySelector('.wcf-bump-order-desc p');
        if (descElement) {
            var br = descElement.querySelector('br');
            if (br) br.style.display = 'none';
            
            // Esconder o preço original na descrição
            var precoOriginal = descElement.querySelector('.wcf-normal-price');
            if (precoOriginal) {
                // Remover completamente o preço original
                precoOriginal.parentNode.removeChild(precoOriginal);
            }
        }
        
        // Procurar e remover qualquer outro preço duplicado na página
        var todosPrecos = document.querySelectorAll('.wcf-normal-price');
        if (todosPrecos.length > 1) {
            for (var i = 1; i < todosPrecos.length; i++) {
                todosPrecos[i].parentNode.removeChild(todosPrecos[i]);
            }
        }
        
        console.log("Ajuste do order bump concluído com sucesso");
    }
    
    // Executar a função quando o DOM estiver carregado
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ajustarOrderBump);
    } else {
        ajustarOrderBump();
    }
    
    // Executar novamente após um atraso para garantir que todos os elementos foram carregados
    setTimeout(ajustarOrderBump, 1000);
})();






/**
 * Função para remover os asteriscos (*) dos campos obrigatórios.
 */
function removerAsteriscosObrigatorios() {
    // Seleciona todos os spans com a classe 'required' dentro da área principal do formulário
    // Usamos #customer_details como um container comum, pode ajustar se necessário
    const asteriscos = document.querySelectorAll('#customer_details span.required');
    let contador = 0;

    asteriscos.forEach(span => {
        // Verificação extra: remove apenas se o conteúdo for exatamente '*' (ignorando espaços)
        // E se tiver o atributo aria-hidden="true" (mais específico do WooCommerce)
        if (span.textContent.trim() === '*' && span.getAttribute('aria-hidden') === 'true') {
            span.remove(); // Remove o elemento <span> inteiro
            contador++;
        }
    });

    if (contador > 0) {
        console.log(`Removidos ${contador} asteriscos de campos obrigatórios.`);
    }
}

/**
 * Inicializa a remoção dos asteriscos e observa mudanças futuras.
 */
function inicializarRemocaoAsteriscos() {
    console.log('Inicializando remoção de asteriscos...');

    // 1. Remove na carga inicial
    removerAsteriscosObrigatorios();

    // 2. Remove em atualizações AJAX do WooCommerce (comum)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout init_checkout', function() {
            console.log('Evento de atualização do checkout detectado, removendo asteriscos novamente...');
            // Pequeno delay para garantir que o DOM foi atualizado pelo WooCommerce
            setTimeout(removerAsteriscosObrigatorios, 100);
        });
    } else {
        console.warn('jQuery não encontrado, não é possível observar eventos do WooCommerce de forma ideal.');
    }

    // 3. Usa MutationObserver para robustez máxima contra scripts que adicionam campos depois
    const observerAlvo = document.getElementById('customer_details'); // Observa a área principal
    if (observerAlvo) {
        const observer = new MutationObserver(function(mutationsList) {
            // Otimização simples: Se qualquer nó foi adicionado na subárvore,
            // apenas re-executa a função de remoção.
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Verifica se algum dos nós adicionados (ou seus descendentes) contêm um span.required
                    let precisaVerificar = false;
                    mutation.addedNodes.forEach(node => {
                        // Verifica se o próprio nó é um elemento e contém a classe ou se tem descendentes com a classe
                        if (node.nodeType === Node.ELEMENT_NODE) {
                           if (node.querySelector('span.required')) {
                               precisaVerificar = true;
                           }
                        }
                    });

                    if (precisaVerificar) {
                        console.log('Mutação no DOM detectada (nós adicionados), verificando asteriscos...');
                        // Espera um instante mínimo caso múltiplos scripts estejam agindo
                        setTimeout(removerAsteriscosObrigatorios, 50);
                        // Não precisa continuar checando outras mutações neste ciclo
                        return;
                    }

                }
            }
        });

        observer.observe(observerAlvo, {
            childList: true, // Observa adição/remoção de filhos diretos
            subtree: true    // Observa também toda a subárvore (importante!)
        });
        console.log('MutationObserver configurado para remoção de asteriscos.');

    } else {
        console.warn('Não foi possível encontrar #customer_details para configurar o MutationObserver.');
    }
}

// --- Execução ---
// Garante que o DOM esteja pronto antes de rodar
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', inicializarRemocaoAsteriscos);
} else {
    // DOM já está pronto
    inicializarRemocaoAsteriscos();
}

/**
 * Function to arrange checkout fields: Put email right after name.
 */
function arrangeMyCheckoutFields_FinalAttempt() {
    console.log('Attempting to arrange checkout fields...');

    const nameFieldContainer = document.getElementById('billing_first_name_field');
    const emailFieldContainer = document.getElementById('billing_email_field');
    // Target the specific wrapper within the 'Dados Pessoais' section
    const wrapper = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');

    if (nameFieldContainer && emailFieldContainer && wrapper) {
        // Check if the email field is DIRECTLY after the name field in the current DOM
        if (nameFieldContainer.nextElementSibling !== emailFieldContainer) {
            console.log('Email field NOT immediately after name. MOVING email field.');
            // Move the email field to be the direct next sibling of the name field
            nameFieldContainer.insertAdjacentElement('afterend', emailFieldContainer);
        } else {
            console.log('Email field already positioned correctly after name in HTML.');
        }

        // Ensure consistent full-width styling (helps prevent weird floats/wraps)
        nameFieldContainer.classList.add('form-row-wide');
        nameFieldContainer.classList.remove('form-row-first', 'wcf-column-50');
        emailFieldContainer.classList.add('form-row-wide');
        emailFieldContainer.classList.remove('form-row-fill');

    } else {
        console.error('Could not find all required elements (name, email, or wrapper).');
        if (!nameFieldContainer) console.error('- Name field (billing_first_name_field) missing.');
        if (!emailFieldContainer) console.error('- Email field (billing_email_field) missing.');
        if (!wrapper) console.error('- Wrapper (.woocommerce-billing-fields__field-wrapper) missing.');
    }
}

/**
 * Function to initialize all attempts to fix the layout.
 */
function initializeFieldArrangement() {
    console.log('Initializing field arrangement attempts...');

    // 1. Run immediately on DOM ready
    arrangeMyCheckoutFields_FinalAttempt();

    // 2. Run after a short delay (catch scripts running slightly after DOMContentLoaded)
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 500); // 0.5 seconds
    setTimeout(arrangeMyCheckoutFields_FinalAttempt, 1500); // 1.5 seconds (extra safe)

    // 3. Run on WooCommerce Checkout Update (Handles AJAX changes)
    if (window.jQuery) {
        jQuery(document.body).on('updated_checkout', function() {
            console.log('WooCommerce "updated_checkout" event triggered. Re-arranging fields...');
            arrangeMyCheckoutFields_FinalAttempt();
        });
    } else {
        console.warn('jQuery not found, cannot bind to WooCommerce update events reliably.');
    }

    // 4. Use MutationObserver to watch for ANY changes within the wrapper
    //    This is the strongest defense against other scripts rearranging things later.
    const targetWrapperNode = document.querySelector('.woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper');
    if (targetWrapperNode) {
        console.log('Setting up MutationObserver to watch the billing fields wrapper.');
        const observerConfig = {
            childList: true // Watch for additions/removals/reordering of direct children
        };

        const observerCallback = function(mutationsList, observer) {
            // We don't need to inspect the mutations, just re-run our check
            console.log('Mutation detected in wrapper. Re-checking field order...');
            arrangeMyCheckoutFields_FinalAttempt();
        };

        const observer = new MutationObserver(observerCallback);
        observer.observe(targetWrapperNode, observerConfig);

        // Optional: You might want to disconnect the observer later if needed,
        // but for a checkout page, letting it run is usually fine.
        // Example: window.addEventListener('beforeunload', () => observer.disconnect());

    } else {
        console.error('Could not find wrapper node to attach MutationObserver.');
    }
}

// --- Start Execution ---
// Run the initialization function when the DOM is ready
if (document.readyState === 'loading') { // Loading hasn't finished yet
    document.addEventListener('DOMContentLoaded', initializeFieldArrangement);
} else { // `DOMContentLoaded` has already fired
    initializeFieldArrangement();
}

document.addEventListener('DOMContentLoaded', function() {
    // Função para remover asteriscos dos placeholders
    function removePlaceholderAsterisks() {
        const inputs = document.querySelectorAll('input[placeholder]');
        inputs.forEach(input => {
            let newPlaceholder = input.placeholder.replace(/\s*\*\s*/g, '');
            input.placeholder = newPlaceholder;
            
            // Observa mudanças no atributo placeholder
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.target.placeholder.includes('*')) {
                        mutation.target.placeholder = mutation.target.placeholder.replace(/\s*\*\s*/g, '');
                    }
                });
            });

            observer.observe(input, {
                attributes: true,
                attributeFilter: ['placeholder']
            });
        });
    }

    // Executa inicialmente
    removePlaceholderAsterisks();

    // Observa mudanças no DOM para novos inputs
    const domObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                removePlaceholderAsterisks();
            }
        });
    });

    domObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
});


document.addEventListener('DOMContentLoaded', function() {
    // Função para mudar o texto
    function changeSecurityCodeText() {
        // Procura todos os elementos com a classe mp-input-label
        const labels = document.querySelectorAll('.mp-input-label');
        
        // Itera sobre cada elemento encontrado
        labels.forEach(label => {
            // Verifica se o texto contém "Código de segurança"
            if (label.textContent.includes('Código de segurança')) {
                // Substitui mantendo o asterisco vermelho
                label.innerHTML = 'CVV<b style="color: red;">*</b>';
            }
        });
    }

    // Executa a função inicialmente
    changeSecurityCodeText();

    // Observa mudanças no DOM para casos de carregamento dinâmico
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                changeSecurityCodeText();
            }
        });
    });

    // Configura o observer para observar todo o documento
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Adiciona listener para eventos de alteração do método de pagamento
    const paymentInputs = document.querySelectorAll('input[name="payment_method"]');
    paymentInputs.forEach(input => {
        input.addEventListener('change', function() {
            setTimeout(changeSecurityCodeText, 100);
        });
    });
});


document.addEventListener('DOMContentLoaded', function () {
    // Selecionar o campo de CEP
    const cepField = document.querySelector('#billing_postcode');
    const cepFieldWrapper = document.querySelector('#billing_postcode_field');

    if (cepField && cepFieldWrapper) {
        // Remover atributos de validação
        cepField.removeAttribute('required');
        cepField.removeAttribute('aria-required');
        cepFieldWrapper.classList.remove('validate-required');

        // Interceptar o envio do formulário
        const checkoutForm = document.querySelector('form.checkout');
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', function (e) {
                // Preencher o CEP com um valor válido para ignorar a validação
                cepField.value = '00000-000';
            });
        }

        // Opcional: Ocultar o campo visualmente
        cepFieldWrapper.style.display = 'none';
    }
});
